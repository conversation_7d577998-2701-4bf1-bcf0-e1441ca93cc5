import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { blogs, blogMonetization, blogViews } from "@/lib/db/schema";
import { eq, and, desc, count, sum, sql } from "drizzle-orm";

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, message: "Unauthorized" },
        { status: 401 }
      );
    }

    const userId = session.user.id;

    // Get all user's blogs with basic info
    const userBlogs = await db.query.blogs.findMany({
      where: eq(blogs.authorId, userId),
      orderBy: [desc(blogs.createdAt)],
      with: {
        monetization: true,
      },
    });

    if (userBlogs.length === 0) {
      return NextResponse.json({
        success: true,
        blogs: [],
        overview: {
          totalBlogs: 0,
          totalViews: 0,
          totalUniqueViews: 0,
          totalEarnings: "0.00",
          averageViewsPerBlog: 0,
          topPerformingBlog: null,
        },
      });
    }

    // Get analytics for each blog
    const blogAnalytics = await Promise.all(
      userBlogs.map(async (blog) => {
        // Get view analytics
        const now = new Date();
        const todayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        const weekStart = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        const monthStart = new Date(now.getFullYear(), now.getMonth(), 1);

        // Total views
        const totalViewsResult = await db
          .select({ count: count() })
          .from(blogViews)
          .where(and(eq(blogViews.blogId, blog.id), eq(blogViews.isBot, false)));

        // Unique views
        const uniqueViewsResult = await db
          .select({ count: count() })
          .from(blogViews)
          .where(and(
            eq(blogViews.blogId, blog.id),
            eq(blogViews.isBot, false),
            eq(blogViews.isUnique, true)
          ));

        // Today's views
        const todayViewsResult = await db
          .select({ count: count() })
          .from(blogViews)
          .where(and(
            eq(blogViews.blogId, blog.id),
            eq(blogViews.isBot, false),
            sql`${blogViews.viewedAt} >= ${todayStart}`
          ));

        // Weekly views
        const weeklyViewsResult = await db
          .select({ count: count() })
          .from(blogViews)
          .where(and(
            eq(blogViews.blogId, blog.id),
            eq(blogViews.isBot, false),
            sql`${blogViews.viewedAt} >= ${weekStart}`
          ));

        // Monthly views
        const monthlyViewsResult = await db
          .select({ count: count() })
          .from(blogViews)
          .where(and(
            eq(blogViews.blogId, blog.id),
            eq(blogViews.isBot, false),
            sql`${blogViews.viewedAt} >= ${monthStart}`
          ));

        // Device breakdown
        const deviceBreakdownResult = await db
          .select({
            device: blogViews.device,
            count: count(),
          })
          .from(blogViews)
          .where(and(eq(blogViews.blogId, blog.id), eq(blogViews.isBot, false)))
          .groupBy(blogViews.device);

        // Top countries
        const topCountriesResult = await db
          .select({
            country: blogViews.country,
            count: count(),
          })
          .from(blogViews)
          .where(and(eq(blogViews.blogId, blog.id), eq(blogViews.isBot, false)))
          .groupBy(blogViews.country)
          .orderBy(desc(count()))
          .limit(5);

        return {
          id: blog.id,
          title: blog.title,
          slug: blog.slug,
          viewCount: blog.viewCount || 0,
          totalViews: totalViewsResult[0]?.count || 0,
          uniqueViews: uniqueViewsResult[0]?.count || 0,
          todayViews: todayViewsResult[0]?.count || 0,
          weeklyViews: weeklyViewsResult[0]?.count || 0,
          monthlyViews: monthlyViewsResult[0]?.count || 0,
          monetization: blog.monetization ? {
            status: blog.monetization.status,
            totalEarnings: blog.monetization.totalEarnings || "0.00",
            uniqueReads: blog.monetization.uniqueReads || 0,
            cprRate: blog.monetization.cprRate || "0.00",
          } : undefined,
          deviceBreakdown: deviceBreakdownResult.map(d => ({
            device: d.device || 'unknown',
            count: d.count,
          })),
          topCountries: topCountriesResult.map(c => ({
            country: c.country || 'unknown',
            count: c.count,
          })),
          createdAt: blog.createdAt.toISOString(),
        };
      })
    );

    // Calculate overview statistics
    const totalBlogs = userBlogs.length;
    const totalViews = blogAnalytics.reduce((sum, blog) => sum + blog.totalViews, 0);
    const totalUniqueViews = blogAnalytics.reduce((sum, blog) => sum + blog.uniqueViews, 0);
    const totalEarnings = blogAnalytics.reduce((sum, blog) => {
      if (blog.monetization) {
        return sum + parseFloat(blog.monetization.totalEarnings);
      }
      return sum;
    }, 0);
    const averageViewsPerBlog = totalBlogs > 0 ? Math.round(totalViews / totalBlogs) : 0;

    // Find top performing blog
    const topPerformingBlog = blogAnalytics.reduce((top, current) => {
      if (!top || current.totalViews > top.totalViews) {
        return {
          title: current.title,
          slug: current.slug,
          views: current.totalViews,
        };
      }
      return top;
    }, null as { title: string; slug: string; views: number } | null);

    const overview = {
      totalBlogs,
      totalViews,
      totalUniqueViews,
      totalEarnings: totalEarnings.toFixed(2),
      averageViewsPerBlog,
      topPerformingBlog,
    };

    return NextResponse.json({
      success: true,
      blogs: blogAnalytics,
      overview,
    });

  } catch (error) {
    console.error("Error fetching blog analytics:", error);
    return NextResponse.json(
      { success: false, message: "Internal server error" },
      { status: 500 }
    );
  }
}
