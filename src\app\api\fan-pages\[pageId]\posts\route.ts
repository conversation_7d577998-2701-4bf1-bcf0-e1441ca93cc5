import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import {
  fanPages,
  fanPagePosts,
  fanPageRoles,
  fanPagePostLikes,
  fanPagePostComments,
  users,
  notifications,
  fanPageFollowers
} from "@/lib/db/schema";
import { eq, and, desc, count, inArray } from "drizzle-orm";
import { z } from "zod";
import { v4 as uuidv4 } from "uuid";

const createPostSchema = z.object({
  content: z.string().max(5000).optional().default(''),
  images: z.array(z.string()).optional(),
  videos: z.array(z.string()).optional(),
  type: z.enum(['text', 'image', 'video', 'link', 'event']).default('text'),

}).refine(data => {
  // Either content, images, or videos must be provided
  return (data.content && data.content.trim().length > 0) ||
         (data.images && data.images.length > 0) ||
         (data.videos && data.videos.length > 0);
}, {
  message: "Post must contain either text content, images, or videos",
  path: ["content"],
});

interface RouteParams {
  params: Promise<{
    pageId: string;
  }>;
}

// GET /api/fan-pages/[pageId]/posts - Get fan page posts
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const { pageId } = await params;

    if (!pageId || pageId.trim() === '') {
      return NextResponse.json(
        { error: "Invalid page ID" },
        { status: 400 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = Math.max(1, parseInt(searchParams.get("page") || "1"));
    const limit = Math.min(20, Math.max(5, parseInt(searchParams.get("limit") || "10"))); // Limit between 5-20
    const offset = (page - 1) * limit;

    const session = await getServerSession(authOptions);
    const currentUserId = session?.user?.id;

    // Check if fan page exists
    const pageResult = await db
      .select()
      .from(fanPages)
      .where(eq(fanPages.id, pageId))
      .limit(1);

    if (pageResult.length === 0) {
      return NextResponse.json(
        { error: "Fan page not found" },
        { status: 404 }
      );
    }

    // Get posts with fan page info
    const posts = await db
      .select({
        id: fanPagePosts.id,
        content: fanPagePosts.content,
        images: fanPagePosts.images,
        videos: fanPagePosts.videos,
        type: fanPagePosts.type,
        likeCount: fanPagePosts.likeCount,
        commentCount: fanPagePosts.commentCount,
        shareCount: fanPagePosts.shareCount,
        viewCount: fanPagePosts.viewCount,
        createdAt: fanPagePosts.createdAt,
        fanPage: {
          id: fanPages.id,
          ownerId: fanPages.ownerId,
          name: fanPages.name,
          username: fanPages.username,
          profileImage: fanPages.profileImage,
          isVerified: fanPages.isVerified,
        },
      })
      .from(fanPagePosts)
      .leftJoin(fanPages, eq(fanPagePosts.fanPageId, fanPages.id))
      .where(
        and(
          eq(fanPagePosts.fanPageId, pageId),
          eq(fanPagePosts.isPublished, true)
        )
      )
      .orderBy(desc(fanPagePosts.createdAt))
      .limit(limit)
      .offset(offset);

    // Get user's like/dislike status for each post if user is logged in
    let postsWithUserStatus = posts;
    if (currentUserId) {
      const postIds = posts.map(post => post.id);

      if (postIds.length > 0) {
        const userLikes = await db
          .select({
            fanPagePostId: fanPagePostLikes.fanPagePostId,
            type: fanPagePostLikes.type,
          })
          .from(fanPagePostLikes)
          .where(
            and(
              eq(fanPagePostLikes.userId, currentUserId),
              inArray(fanPagePostLikes.fanPagePostId, postIds)
            )
          );

        // Create a map for quick lookup
        const likesMap = new Map();
        userLikes.forEach(like => {
          likesMap.set(like.fanPagePostId, like.type);
        });

        // Add user status to posts
        postsWithUserStatus = posts.map(post => ({
          ...post,
          liked: likesMap.get(post.id) === 'like',
          disliked: likesMap.get(post.id) === 'angry',
        }));
      }
    } else {
      // Add default false values for non-authenticated users
      postsWithUserStatus = posts.map(post => ({
        ...post,
        liked: false,
        disliked: false,
      }));
    }

    // Get total count
    const totalResult = await db
      .select({ count: count() })
      .from(fanPagePosts)
      .where(
        and(
          eq(fanPagePosts.fanPageId, pageId),
          eq(fanPagePosts.isPublished, true)
        )
      );

    const total = totalResult[0]?.count || 0;
    const totalPages = Math.ceil(total / limit);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    return NextResponse.json({
      posts: postsWithUserStatus,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNextPage,
        hasPrevPage,
        count: postsWithUserStatus.length,
      },
    });

  } catch (error) {
    console.error("Error fetching fan page posts:", error);
    return NextResponse.json(
      { error: "Failed to fetch posts" },
      { status: 500 }
    );
  }
}

// POST /api/fan-pages/[pageId]/posts - Create a new post
export async function POST(request: NextRequest, { params }: RouteParams) {
  try {
    const { pageId } = await params;
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if fan page exists
    const pageResult = await db
      .select()
      .from(fanPages)
      .where(eq(fanPages.id, pageId))
      .limit(1);

    if (pageResult.length === 0) {
      return NextResponse.json(
        { error: "Fan page not found" },
        { status: 404 }
      );
    }

    const page = pageResult[0];
    const isOwner = page.ownerId === session.user.id;

    // Check if user has permission to post
    let hasPostPermission = isOwner;
    if (!isOwner) {
      const roleResult = await db
        .select()
        .from(fanPageRoles)
        .where(
          and(
            eq(fanPageRoles.userId, session.user.id),
            eq(fanPageRoles.fanPageId, pageId)
          )
        )
        .limit(1);

      hasPostPermission = roleResult.length > 0;
    }

    if (!hasPostPermission) {
      return NextResponse.json(
        { error: "You don't have permission to post on this page" },
        { status: 403 }
      );
    }

    const body = await request.json();
    const validatedData = createPostSchema.parse(body);

    // Create the post
    const postId = uuidv4();

    await db.insert(fanPagePosts).values({
      id: postId,
      fanPageId: pageId,
      content: validatedData.content,
      images: validatedData.images || [],
      videos: validatedData.videos || [],
      type: validatedData.type,
      scheduledAt: null,
      isPublished: true,
    });

    // Update post count
    await db
      .update(fanPages)
      .set({
        postCount: page.postCount + 1,
        updatedAt: new Date(),
      })
      .where(eq(fanPages.id, pageId));

    // If published immediately, notify followers
    if (isPublished) {
      // Get all followers
      const followers = await db
        .select({ userId: fanPageFollowers.userId })
        .from(fanPageFollowers)
        .where(eq(fanPageFollowers.fanPageId, pageId));

      // Create notifications for followers (batch insert)
      if (followers.length > 0) {
        const notificationData = followers.map(follower => ({
          id: uuidv4(),
          recipientId: follower.userId,
          type: "fan_page_post" as const,
          fanPageId: pageId,
          fanPagePostId: postId,
        }));

        await db.insert(notifications).values(notificationData);
      }
    }

    // Fetch the created post with page info
    const newPost = await db
      .select({
        id: fanPagePosts.id,
        content: fanPagePosts.content,
        images: fanPagePosts.images,
        videos: fanPagePosts.videos,
        type: fanPagePosts.type,
        scheduledAt: fanPagePosts.scheduledAt,
        isPublished: fanPagePosts.isPublished,
        likeCount: fanPagePosts.likeCount,
        commentCount: fanPagePosts.commentCount,
        shareCount: fanPagePosts.shareCount,
        viewCount: fanPagePosts.viewCount,
        createdAt: fanPagePosts.createdAt,
        fanPage: {
          id: fanPages.id,
          ownerId: fanPages.ownerId,
          name: fanPages.name,
          username: fanPages.username,
          profileImage: fanPages.profileImage,
          isVerified: fanPages.isVerified,
        },
      })
      .from(fanPagePosts)
      .leftJoin(fanPages, eq(fanPagePosts.fanPageId, fanPages.id))
      .where(eq(fanPagePosts.id, postId))
      .limit(1);

    return NextResponse.json({
      message: "Post created successfully",
      post: newPost[0],
    });

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid data", details: error.errors },
        { status: 400 }
      );
    }

    console.error("Error creating fan page post:", error);
    return NextResponse.json(
      { error: "Failed to create post" },
      { status: 500 }
    );
  }
}
